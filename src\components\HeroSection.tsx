
import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/contexts/LanguageContext";

export default function HeroSection() {
  const { t } = useLanguage();
  const [scrollY, setScrollY] = useState(0);



  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Calculate parallax effect
  const backgroundY = scrollY * 0.5;
  const contentY = scrollY * 0.2;

  // Add defensive coding to prevent accessing undefined properties
  const heroTitle = t?.hero?.title || "Specialised Care for Brain, Spine and Peripheral Nerve Conditions";
  const heroSubtitle = t?.hero?.subtitle || "COMPREHENSIVE NEUROSURGICAL SERVICES";
  const heroDescription = t?.hero?.description || "Dr <PERSON> provides expert diagnosis and treatment for a wide range of neurological conditions.";
  const bookConsultation = t?.hero?.bookConsultation || "Book Consultation";
  const exploreTreatments = t?.hero?.exploreTreatments || "Explore Treatments";
  const scrollDown = t?.hero?.scrollDown || "Scroll Down";

  return (
    <section className="relative min-h-screen flex items-center overflow-hidden">
      {/* Background image with parallax effect */}
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: "url('/images/hero-bg.jpg')",
          transform: `translateY(${backgroundY}px)`,
        }}
      ></div>

      {/* Dark overlay */}
      <div className="absolute inset-0 bg-black/50"></div>

      {/* Content */}
      <div
        className="container relative z-10 pt-20"
        style={{
          transform: `translateY(${contentY}px)`,
        }}
      >
        <div className="max-w-3xl">
          <p className="text-primary font-medium mb-2">{heroSubtitle}</p>
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
            {heroTitle}
          </h1>
          <p className="text-lg text-white/80 mb-8">
            {heroDescription}
          </p>
          <div className="flex flex-wrap gap-4">
            <Button asChild size="lg" className="rounded-full">
              <Link to="/contact">{bookConsultation}</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="rounded-full text-white border-white hover:bg-white/20 hover:text-white">
              <Link to="/expertise">{exploreTreatments}</Link>
            </Button>
          </div>
        </div>
      </div>

      {/* Scroll down indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white text-center">
        <p className="text-sm mb-2">{scrollDown}</p>
        <ChevronDown className="mx-auto animate-bounce" />
      </div>
    </section>
  );
}
