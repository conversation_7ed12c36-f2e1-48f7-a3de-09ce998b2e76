import { useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import PageHeader from "@/components/PageHeader";
import Layout from "@/components/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { AlertTriangle, Phone, Clock, Stethoscope, Brain, Activity } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import SafeImage from "@/components/SafeImage";

export default function Emergencies() {
  const { t } = useLanguage();
  
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Emergency case examples
  const emergencyCases = [
    {
      title: "Acute Spinal Cord Compression",
      symptoms: "Progressive weakness, sensory changes, bowel/bladder dysfunction",
      action: "Immediate referral to emergency department and contact our on-call neurosurgeon",
      timeframe: "Immediate - same day",
      contact: "Emergency hotline: (03) 9008 4200"
    },
    {
      title: "Cauda Equina Syndrome",
      symptoms: "Saddle anaesthesia, urinary retention, bilateral sciatica, leg weakness",
      action: "Immediate referral to emergency department and contact our on-call neurosurgeon",
      timeframe: "Immediate - same day",
      contact: "Emergency hotline: (03) 9008 4200"
    },
    {
      title: "Acute Intracranial Haemorrhage",
      symptoms: "Sudden severe headache, altered consciousness, focal neurological deficits",
      action: "Immediate referral to emergency department",
      timeframe: "Immediate - same day",
      contact: "Emergency department"
    }
  ];

  return (
    <Layout>
      <PageHeader
        title="Neurosurgical Emergencies & Red Flags"
        subtitle="Guidance for GPs on recognising and managing urgent neurosurgical conditions"
        backgroundImage="/images/gp-resources/emergencies-hero.jpg"
      />

      <section className="py-16 bg-background">
        <div className="container">
          <div className="max-w-3xl mx-auto mb-12">
            <p className="text-lg mb-8">
              Early recognition of neurosurgical emergencies is critical for optimal patient outcomes. This guide outlines key red flags, recommended actions, and contact protocols for urgent neurosurgical conditions.
            </p>

            {/* Image grid for visual context */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              <div className="rounded-lg overflow-hidden shadow-md">
                <SafeImage
                  src="https://images.unsplash.com/photo-1516549655169-df83a0774514?auto=format&fit=crop&w=800&q=80"
                  alt="Medical emergency team"
                  className="w-full h-64 object-cover"
                  fallbackSrc="/images/gp-resources/emergencies-1.jpg"
                />
              </div>
              <div className="rounded-lg overflow-hidden shadow-md">
                <SafeImage
                  src="https://images.unsplash.com/photo-1538108149393-fbbd81895907?auto=format&fit=crop&w=800&q=80"
                  alt="Emergency medical care"
                  className="w-full h-64 object-cover"
                  fallbackSrc="/images/gp-resources/emergencies-2.jpg"
                />
              </div>
            </div>

            <div className="bg-destructive/10 border border-destructive/30 rounded-lg p-6 mb-8">
              <div className="flex items-start">
                <AlertTriangle className="h-6 w-6 text-destructive mr-3 mt-1" />
                <div>
                  <h3 className="font-semibold text-lg mb-2">Emergency Contact Protocol</h3>
                  <p className="mb-4">For urgent neurosurgical advice or to arrange immediate assessment:</p>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>During business hours (8:30am-5:00pm): Call (03) 9008 4200</li>
                    <li>After hours: Call (03) 9008 4200 and follow prompts for the on-call neurosurgeon</li>
                    <li>For immediate life-threatening emergencies: Direct patient to nearest emergency department and call ahead to alert the ED staff</li>
                  </ul>
                </div>
              </div>
            </div>

            <Tabs defaultValue="cranial" className="w-full">
              <TabsList className="grid w-full grid-cols-2 md:grid-cols-3">
                <TabsTrigger value="cranial">Cranial Emergencies</TabsTrigger>
                <TabsTrigger value="spinal">Spinal Emergencies</TabsTrigger>
                <TabsTrigger value="postop">Post-operative Concerns</TabsTrigger>
              </TabsList>

              <TabsContent value="cranial" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Cranial Emergencies</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      <div className="p-4 border border-primary/20 rounded-md">
                        <h3 className="font-semibold mb-2">Acute Intracranial Haemorrhage</h3>
                        <p className="mb-2 text-sm">Symptoms: Sudden severe headache, altered consciousness, focal neurological deficits, nausea/vomiting</p>
                        <p className="mb-2 text-sm">Action: Immediate referral to emergency department</p>
                        <p className="text-sm text-destructive">Timeframe: Immediate - same day</p>
                      </div>
                      
                      <div className="p-4 border border-primary/20 rounded-md">
                        <h3 className="font-semibold mb-2">Acute Hydrocephalus</h3>
                        <p className="mb-2 text-sm">Symptoms: Headache, vomiting, altered consciousness, gait disturbance, urinary incontinence</p>
                        <p className="mb-2 text-sm">Action: Urgent referral to emergency department</p>
                        <p className="text-sm text-destructive">Timeframe: Immediate - same day</p>
                      </div>
                      
                      <div className="p-4 border border-primary/20 rounded-md">
                        <h3 className="font-semibold mb-2">Malignant Cerebral Oedema</h3>
                        <p className="mb-2 text-sm">Symptoms: Progressive neurological deterioration, headache, altered consciousness</p>
                        <p className="mb-2 text-sm">Action: Immediate referral to emergency department</p>
                        <p className="text-sm text-destructive">Timeframe: Immediate - same day</p>
                      </div>
                    </div>

                    <div className="mt-6">
                      <SafeImage
                        src="https://images.unsplash.com/photo-1579154341098-e4e158cc7f55?auto=format&fit=crop&w=800&q=80"
                        alt="Brain scan review"
                        className="w-full rounded-md object-cover h-64"
                        fallbackSrc="/images/gp-resources/brain-scan.jpg"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="spinal" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Spinal Emergencies</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      <div className="p-4 border border-primary/20 rounded-md">
                        <h3 className="font-semibold mb-2">Cauda Equina Syndrome</h3>
                        <p className="mb-2 text-sm">Symptoms: Saddle anaesthesia, urinary retention, bilateral sciatica, leg weakness</p>
                        <p className="mb-2 text-sm">Action: Immediate referral to emergency department and contact our on-call neurosurgeon</p>
                        <p className="text-sm text-destructive">Timeframe: Immediate - same day</p>
                      </div>
                      
                      <div className="p-4 border border-primary/20 rounded-md">
                        <h3 className="font-semibold mb-2">Acute Spinal Cord Compression</h3>
                        <p className="mb-2 text-sm">Symptoms: Progressive weakness, sensory changes, bowel/bladder dysfunction</p>
                        <p className="mb-2 text-sm">Action: Immediate referral to emergency department and contact our on-call neurosurgeon</p>
                        <p className="text-sm text-destructive">Timeframe: Immediate - same day</p>
                      </div>
                      
                      <div className="p-4 border border-primary/20 rounded-md">
                        <h3 className="font-semibold mb-2">Spinal Epidural Abscess</h3>
                        <p className="mb-2 text-sm">Symptoms: Back pain, fever, progressive neurological deficits</p>
                        <p className="mb-2 text-sm">Action: Urgent referral to emergency department</p>
                        <p className="text-sm text-destructive">Timeframe: Immediate - same day</p>
                      </div>
                    </div>

                    <div className="mt-6">
                      <SafeImage
                        src="https://images.unsplash.com/photo-1576671414121-aa2d60f1e5b4?auto=format&fit=crop&w=800&q=80"
                        alt="Spine MRI review"
                        className="w-full rounded-md object-cover h-64"
                        fallbackSrc="/images/gp-resources/spine-scan.jpg"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="postop" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Post-operative Concerns</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      <div className="p-4 border border-primary/20 rounded-md">
                        <h3 className="font-semibold mb-2">Wound Infection</h3>
                        <p className="mb-2 text-sm">Symptoms: Increasing pain, redness, swelling, discharge, fever</p>
                        <p className="mb-2 text-sm">Action: Contact our rooms for same-day assessment</p>
                        <p className="text-sm text-amber-600">Timeframe: Same day - 24 hours</p>
                      </div>
                      
                      <div className="p-4 border border-primary/20 rounded-md">
                        <h3 className="font-semibold mb-2">New or Worsening Neurological Deficits</h3>
                        <p className="mb-2 text-sm">Symptoms: New weakness, sensory changes, coordination problems</p>
                        <p className="mb-2 text-sm">Action: Contact our rooms for urgent assessment</p>
                        <p className="text-sm text-destructive">Timeframe: Immediate - same day</p>
                      </div>
                      
                      <div className="p-4 border border-primary/20 rounded-md">
                        <h3 className="font-semibold mb-2">CSF Leak</h3>
                        <p className="mb-2 text-sm">Symptoms: Clear fluid from wound or nose, headache worse when upright</p>
                        <p className="mb-2 text-sm">Action: Contact our rooms for urgent assessment</p>
                        <p className="text-sm text-amber-600">Timeframe: Same day - 24 hours</p>
                      </div>
                    </div>

                    <div className="mt-6">
                      <SafeImage
                        src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?auto=format&fit=crop&w=800&q=80"
                        alt="Post-operative care"
                        className="w-full rounded-md object-cover h-64"
                        fallbackSrc="/images/gp-resources/postop-care.jpg"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Additional visual content */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
            <div className="rounded-lg overflow-hidden shadow-md">
              <SafeImage
                src="https://images.unsplash.com/photo-1551601651-2a8555f1a136?auto=format&fit=crop&w=800&q=80"
                alt="Emergency response team"
                className="w-full h-48 object-cover"
                fallbackSrc="/images/gp-resources/emergency-team.jpg"
              />
              <div className="p-4 bg-card">
                <h3 className="font-semibold mb-2">Rapid Response Protocol</h3>
                <p className="text-sm text-muted-foreground">Our dedicated emergency response team is available 24/7 to provide immediate neurosurgical consultation.</p>
              </div>
            </div>
            <div className="rounded-lg overflow-hidden shadow-md">
              <SafeImage
                src="https://images.unsplash.com/photo-1581093588401-fbb62a02f120?auto=format&fit=crop&w=800&q=80"
                alt="Neurological examination"
                className="w-full h-48 object-cover"
                fallbackSrc="/images/gp-resources/neuro-exam.jpg"
              />
              <div className="p-4 bg-card">
                <h3 className="font-semibold mb-2">Clinical Assessment Tools</h3>
                <p className="text-sm text-muted-foreground">We provide GPs with standardised assessment tools to help identify neurosurgical emergencies.</p>
              </div>
            </div>
            <div className="rounded-lg overflow-hidden shadow-md">
              <SafeImage
                src="https://images.unsplash.com/photo-1582719471384-894fbb16e074?auto=format&fit=crop&w=800&q=80"
                alt="Telehealth consultation"
                className="w-full h-48 object-cover"
                fallbackSrc="/images/gp-resources/telehealth.jpg"
              />
              <div className="p-4 bg-card">
                <h3 className="font-semibold mb-2">Telehealth Triage</h3>
                <p className="text-sm text-muted-foreground">For remote or regional areas, we offer urgent telehealth consultations to assist with initial assessment and triage.</p>
              </div>
            </div>
          </div>

          <div className="mt-16">
            <h2 className="text-3xl font-bold mb-8 text-center">Emergency Case Examples</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {emergencyCases.map((caseItem, index) => (
                <Card key={index} className="border-l-4 border-l-destructive">
                  <CardHeader>
                    <CardTitle className="text-lg">{caseItem.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="mb-2"><span className="font-semibold">Symptoms:</span> {caseItem.symptoms}</p>
                    <p className="mb-2"><span className="font-semibold">Action:</span> {caseItem.action}</p>
                    <p className="mb-2"><span className="font-semibold">Timeframe:</span> <span className="text-destructive">{caseItem.timeframe}</span></p>
                    <p><span className="font-semibold">Contact:</span> {caseItem.contact}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <div className="mt-16 text-center">
            <Button asChild size="lg">
              <Link to="/contact">Contact Our Team</Link>
            </Button>
          </div>
        </div>
      </section>
    </Layout>
  );
}
