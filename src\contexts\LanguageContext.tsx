
import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { en } from '../locales/en';
import { zh } from '../locales/zh';

type Translations = typeof en;

export type SupportedLanguage = 'en' | 'zh';

interface LanguageContextType {
  language: SupportedLanguage;
  setLanguage: (lang: SupportedLanguage) => void;
  t: Translations;
  isLanguageLoaded: boolean;
}

// Ensure translations are properly initialized
const translations: Record<SupportedLanguage, Translations> = {
  en,
  zh
};


const supportedLanguages: SupportedLanguage[] = ['en', 'zh'];

// Create context with default values to prevent undefined
const defaultContext: LanguageContextType = {
  language: 'en',
  setLanguage: () => {},
  t: en, // Use English as fallback
  isLanguageLoaded: false
};

const LanguageContext = createContext<LanguageContextType>(defaultContext);

export const LanguageProvider = ({ children }: { children: ReactNode }) => {
  const [language, setLanguage] = useState<SupportedLanguage>('en');
  const [t, setT] = useState<Translations>(translations.en);
  const [isLanguageLoaded, setIsLanguageLoaded] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();



  // Detect language from URL or localStorage on initial load
  useEffect(() => {
    try {
      const detectLanguage = () => {
        // First check URL path for language
        const pathSegments = location.pathname.split('/').filter(Boolean);
        const langFromUrl = pathSegments[0];

        if (langFromUrl && supportedLanguages.includes(langFromUrl as SupportedLanguage)) {
          return langFromUrl as SupportedLanguage;
        }

        // Then check localStorage
        const savedLanguage = localStorage.getItem('language');
        if (savedLanguage && supportedLanguages.includes(savedLanguage as SupportedLanguage)) {
          return savedLanguage as SupportedLanguage;
        }

        // Finally check browser language
        const browserLang = navigator.language.split('-')[0];
        if (browserLang && supportedLanguages.includes(browserLang as SupportedLanguage)) {
          return browserLang as SupportedLanguage;
        }

        // Default to English
        return 'en';
      };

      const detectedLanguage = detectLanguage();
      setLanguage(detectedLanguage);

      // Ensure we have valid translations
      if (translations[detectedLanguage]) {
        setT(translations[detectedLanguage]);
      } else {
        console.warn(`No translations found for ${detectedLanguage}, falling back to English`);
        setT(translations.en);
      }

      localStorage.setItem('language', detectedLanguage);
      setIsLanguageLoaded(true);
    } catch (error) {
      console.error("Error in language detection:", error);
      // Fallback to English in case of error
      setLanguage('en');
      setT(translations.en);
      setIsLanguageLoaded(true);
    }
  }, [location.pathname]);

  // Change language and update URL if needed
  const changeLanguage = (lang: SupportedLanguage) => {
    try {
      if (translations[lang]) {
        setLanguage(lang);
        setT(translations[lang]);
        localStorage.setItem('language', lang);

        // Update URL to reflect language change if not already at that language
        const pathSegments = location.pathname.split('/').filter(Boolean);
        const currentUrlLang = pathSegments[0];

        if (supportedLanguages.includes(currentUrlLang as SupportedLanguage)) {
          // URL already has a language prefix, update it
          if (currentUrlLang !== lang) {
            const newPath = '/' + lang + location.pathname.substring(currentUrlLang.length + 1);
            navigate(newPath + location.search + location.hash);
          }
        } else if (lang !== 'en') {
          // URL has no language prefix and selected language is not default, add prefix
          navigate('/' + lang + location.pathname + location.search + location.hash);
        }
      } else {
        console.warn(`No translations found for ${lang}, not changing language`);
      }
    } catch (error) {
      console.error("Error changing language:", error);
    }
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage: changeLanguage, t, isLanguageLoaded }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook with error handling
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    console.error("useLanguage must be used within a LanguageProvider");
    // Return default values instead of throwing error
    return defaultContext;
  }
  return context;
};
