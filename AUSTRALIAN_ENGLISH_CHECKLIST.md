# Australian English Style Guide for Codebase Review

## Common American to Australian English Spelling Conversions

| American English | Australian English |
|-----------------|-------------------|
| analyze         | analyse           |
| anesthesia      | anaesthesia       |
| center          | centre            |
| color           | colour            |
| defense         | defence           |
| dialog          | dialogue          |
| edema           | oedema            |
| encyclopedia    | encyclopaedia     |
| endeavor        | endeavour         |
| estrogen        | oestrogen         |
| favor           | favour            |
| fiber           | fibre             |
| hemorrhage      | haemorrhage       |
| honor           | honour            |
| humor           | humour            |
| labor           | labour            |
| leukemia        | leukaemia         |
| license (noun)  | licence (noun)    |
| liter           | litre             |
| meter           | metre             |
| neighbor        | neighbour         |
| optimize        | optimise          |
| organization    | organisation      |
| pediatric       | paediatric        |
| practice (verb) | practise (verb)   |
| program         | programme (except for computer programs) |
| realize         | realise           |
| recognize       | recognise         |
| specialty       | speciality        |
| theater         | theatre           |
| tumor           | tumour            |
| traveling       | travelling        |

## Date Formats
- Use DD/MM/YYYY format (e.g., 25/12/2023) instead of MM/DD/YYYY
- For written dates: 25 December 2023 (not December 25, 2023)

## Time Formats
- Use 24-hour clock (e.g., 14:30) or 12-hour clock with am/pm (e.g., 2:30 pm)
- Note "am" and "pm" are lowercase with no periods

## Measurements
- Use metric system (kilometres, metres, litres, etc.)
- Use Celsius for temperature (e.g., 38°C)

## Terminology
- Use "mobile" instead of "cell phone"
- Use "holiday" instead of "vacation"
- Use "petrol" instead of "gas" (for fuel)
- Use "GP" or "general practitioner" instead of "primary care physician"
- Use "hospital" or "medical centre" instead of "medical center"
- Use "surgery" (as in doctor's office) where appropriate

## Punctuation
- Use single quotation marks for primary quotes: 'like this'
- Use double quotation marks for quotes within quotes: 'He said "hello" to me'
- Place punctuation outside quotation marks unless part of the quoted material

## Currency
- Use $ symbol with AUD specification where needed: $100 AUD
- For international currencies, specify: USD, EUR, etc.

## Phone Numbers
- Format: (03) 9008 4200 for landlines
- Mobile: 0400 123 456

## Addresses
- Use Australian address format:
  - Street number, then street name
  - Suburb, STATE Postcode
  - e.g., 619 Canterbury Road, SURREY HILLS VIC 3127

## Medical Terminology
- Use "theatre" for operating room
- Use "ward" for hospital department
- Use "script" for prescription
- Use "bulk billing" instead of "insurance coverage" where appropriate
- Use "Medicare" for Australia's public health system

## Files to Check Thoroughly
- All content files (*.tsx, *.jsx, *.ts, *.js)
- Translation files (en.ts, etc.)
- Documentation files (*.md)
- Configuration files that might contain user-facing strings